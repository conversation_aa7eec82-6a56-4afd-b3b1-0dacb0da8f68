"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6118],{77498:function(n,e,t){t.d(e,{GV:function(){return i},Gv:function(){return u},Sk:function(){return r},WQ:function(){return c},ZO:function(){return o}});var r="data-focus-lock",o="data-focus-lock-disabled",u="data-no-focus-lock",i="data-autofocus-inside",c="data-no-autofocus"},30376:function(n,e,t){t.d(e,{Z:function(){return l}});var r=t(87462),o=t(67294),u=t(77498),i=t(99495),c={width:"1px",height:"0px",padding:0,overflow:"hidden",position:"fixed",top:"1px",left:"1px"},a=t(83199),d=(0,o.createContext)(void 0),f=[],s=(0,o.forwardRef)(function(n,e){var t,s=(0,o.useState)(),l=s[0],v=s[1],m=(0,o.useRef)(),p=(0,o.useRef)(!1),h=(0,o.useRef)(null),b=(0,o.useState)({})[1],y=n.children,g=n.disabled,E=void 0!==g&&g,w=n.noFocusGuards,x=void 0!==w&&w,N=n.persistentFocus,O=n.crossFrame,k=n.autoFocus,A=(n.allowTextSelection,n.group),M=n.className,F=n.whiteList,S=n.hasPositiveIndices,D=n.shards,L=void 0===D?f:D,I=n.as,T=n.lockProps,C=n.sideCar,P=n.returnFocus,R=void 0!==P&&P,W=n.focusOptions,_=n.onActivation,Z=n.onDeactivation,G=(0,o.useState)({})[0],q=(0,o.useCallback)(function(n){var e=n.captureFocusRestore;if(!h.current){var t,r=null==(t=document)?void 0:t.activeElement;h.current=r,r!==document.body&&(h.current=e(r))}m.current&&_&&_(m.current),p.current=!0,b()},[_]),U=(0,o.useCallback)(function(){p.current=!1,Z&&Z(m.current),b()},[Z]),B=(0,o.useCallback)(function(n){var e=h.current;if(e){var t=("function"==typeof e?e():e)||document.body,r="function"==typeof R?R(t):R;if(r){var o="object"==typeof r?r:void 0;h.current=null,n?Promise.resolve().then(function(){return t.focus(o)}):t.focus(o)}}},[R]),j=(0,o.useCallback)(function(n){p.current&&a.qy.useMedium(n)},[]),H=a.DA.useMedium,V=(0,o.useCallback)(function(n){m.current!==n&&(m.current=n,v(n))},[]),z=(0,r.Z)(((t={})[u.ZO]=E&&"disabled",t[u.Sk]=A,t),void 0===T?{}:T),K=!0!==x,Q=K&&"tail"!==x,Y=(0,i.q)([e,V]),J=(0,o.useMemo)(function(){return{observed:m,shards:L,enabled:!E,active:p.current}},[E,p.current,L,l]);return o.createElement(o.Fragment,null,K&&[o.createElement("div",{key:"guard-first","data-focus-guard":!0,tabIndex:E?-1:0,style:c}),S?o.createElement("div",{key:"guard-nearest","data-focus-guard":!0,tabIndex:E?-1:1,style:c}):null],!E&&o.createElement(C,{id:G,sideCar:a.sH,observed:l,disabled:E,persistentFocus:void 0!==N&&N,crossFrame:void 0===O||O,autoFocus:void 0===k||k,whiteList:F,shards:L,onActivation:q,onDeactivation:U,returnFocus:B,focusOptions:W,noFocusGuards:x}),o.createElement(void 0===I?"div":I,(0,r.Z)({ref:Y},z,{className:M,onBlur:H,onFocus:j}),o.createElement(d.Provider,{value:J},y)),Q&&o.createElement("div",{"data-focus-guard":!0,tabIndex:E?-1:0,style:c}))});s.propTypes={};var l=s},17742:function(n,e,t){t.d(e,{Z:function(){return nR}});var r,o,u=t(67294),i=t(94578),c=t(4942),a=t(77498),d=function(n){for(var e=Array(n.length),t=0;t<n.length;++t)e[t]=n[t];return e},f=function(n){return Array.isArray(n)?n:[n]},s=function(n){return Array.isArray(n)?n[0]:n},l=function(n){if(n.nodeType!==Node.ELEMENT_NODE)return!1;var e=window.getComputedStyle(n,null);return!!e&&!!e.getPropertyValue&&("none"===e.getPropertyValue("display")||"hidden"===e.getPropertyValue("visibility"))},v=function(n){return n.parentNode&&n.parentNode.nodeType===Node.DOCUMENT_FRAGMENT_NODE?n.parentNode.host:n.parentNode},m=function(n){return n===document||n&&n.nodeType===Node.DOCUMENT_NODE},p=function(n,e){var t,r=n.get(e);if(void 0!==r)return r;var o=(t=p.bind(void 0,n),!e||m(e)||!l(e)&&!e.hasAttribute("inert")&&t(v(e)));return n.set(e,o),o},h=function(n,e){var t,r=n.get(e);if(void 0!==r)return r;var o=(t=h.bind(void 0,n),!e||!!m(e)||!!E(e)&&t(v(e)));return n.set(e,o),o},b=function(n){return n.dataset},y=function(n){return"INPUT"===n.tagName},g=function(n){return y(n)&&"radio"===n.type},E=function(n){return![!0,"true",""].includes(n.getAttribute(a.WQ))},w=function(n){var e;return!!(n&&(null===(e=b(n))||void 0===e?void 0:e.focusGuard))},x=function(n){return!w(n)},N=function(n){return!!n},O=function(n,e){var t=Math.max(0,n.tabIndex),r=Math.max(0,e.tabIndex),o=t-r,u=n.index-e.index;if(o){if(!t)return 1;if(!r)return -1}return o||u},k=function(n,e,t){return d(n).map(function(n,e){var r=n.tabIndex<0&&!n.hasAttribute("tabindex")?0:n.tabIndex;return{node:n,index:e,tabIndex:t&&-1===r?(n.dataset||{}).focusGuard?0:-1:r}}).filter(function(n){return!e||n.tabIndex>=0}).sort(O)},A="button:enabled,select:enabled,textarea:enabled,input:enabled,a[href],area[href],summary,iframe,object,embed,audio[controls],video[controls],[tabindex],[contenteditable],[autofocus]",M="".concat(A,", [data-focus-guard]"),F=function(n,e){return d((n.shadowRoot||n).children).reduce(function(n,t){return n.concat(t.matches(e?M:A)?[t]:[],F(t))},[])},S=function(n,e){var t;return n instanceof HTMLIFrameElement&&(null===(t=n.contentDocument)||void 0===t?void 0:t.body)?D([n.contentDocument.body],e):[n]},D=function(n,e){return n.reduce(function(n,t){var r,o=F(t,e),u=(r=[]).concat.apply(r,o.map(function(n){return S(n,e)}));return n.concat(u,t.parentNode?d(t.parentNode.querySelectorAll(A)).filter(function(n){return n===t}):[])},[])},L=function(n,e){return d(n).filter(function(n){return p(e,n)}).filter(function(n){return!((y(n)||"BUTTON"===n.tagName)&&("hidden"===n.type||n.disabled))})},I=function(n,e){return void 0===e&&(e=new Map),d(n).filter(function(n){return h(e,n)})},T=function(n,e,t){return k(L(D(n,t),e),!0,t)},C=function(n,e){return k(L(D(n),e),!1)},P=function(n,e){return n.shadowRoot?P(n.shadowRoot,e):!!(void 0!==Object.getPrototypeOf(n).contains&&Object.getPrototypeOf(n).contains.call(n,e))||d(n.children).some(function(n){var t;if(n instanceof HTMLIFrameElement){var r=null===(t=n.contentDocument)||void 0===t?void 0:t.body;return!!r&&P(r,e)}return P(n,e)})},R=function(n){try{return n()}catch(n){return}},W=function(n){if(void 0===n&&(n=document),n&&n.activeElement){var e=n.activeElement;return e.shadowRoot?W(e.shadowRoot):e instanceof HTMLIFrameElement&&R(function(){return e.contentWindow.document})?W(e.contentWindow.document):e}},_=function(n){void 0===n&&(n=document);var e=W(n);return!!e&&d(n.querySelectorAll("[".concat(a.Gv,"]"))).some(function(n){return P(n,e)})},Z=function(n){for(var e=new Set,t=n.length,r=0;r<t;r+=1)for(var o=r+1;o<t;o+=1){var u=n[r].compareDocumentPosition(n[o]);(u&Node.DOCUMENT_POSITION_CONTAINED_BY)>0&&e.add(o),(u&Node.DOCUMENT_POSITION_CONTAINS)>0&&e.add(r)}return n.filter(function(n,t){return!e.has(t)})},G=function(n){return n.parentNode?G(n.parentNode):n},q=function(n){return f(n).filter(Boolean).reduce(function(n,e){var t=e.getAttribute(a.Sk);return n.push.apply(n,t?Z(d(G(e).querySelectorAll("[".concat(a.Sk,'="').concat(t,'"]:not([').concat(a.ZO,'="disabled"])')))):[e]),n},[])},U=function(n,e){return void 0===e&&(e=W(s(n).ownerDocument)),!!e&&(!e.dataset||!e.dataset.focusGuard)&&q(n).some(function(n){var t;return P(n,e)||(t=e,!!d(n.querySelectorAll("iframe")).some(function(n){return n===t}))})},B=function(n,e){n&&("focus"in n&&n.focus(e),"contentWindow"in n&&n.contentWindow&&n.contentWindow.focus())},j=function(n,e){return g(n)&&n.name&&e.filter(g).filter(function(e){return e.name===n.name}).filter(function(n){return n.checked})[0]||n},H=function(n){var e=new Set;return n.forEach(function(t){return e.add(j(t,n))}),n.filter(function(n){return e.has(n)})},V=function(n){return n[0]&&n.length>1?j(n[0],n):n[0]},z=function(n,e){return n.indexOf(j(e,n))},K="NEW_FOCUS",Q=function(n,e,t,r,o){var u=n.length,i=n[0],c=n[u-1],a=w(r);if(!(r&&n.indexOf(r)>=0)){var d=void 0!==r?t.indexOf(r):-1,f=o?t.indexOf(o):d,s=o?n.indexOf(o):-1;if(-1===d)return -1!==s?s:K;if(-1===s)return K;var l=d-f,v=t.indexOf(i),m=t.indexOf(c),p=H(t),h=void 0!==r?p.indexOf(r):-1,b=o?p.indexOf(o):h,y=p.filter(function(n){return n.tabIndex>=0}),g=void 0!==r?y.indexOf(r):-1,E=o?y.indexOf(o):g;if(!l&&s>=0||0===e.length)return s;var x=z(n,e[0]),N=z(n,e[e.length-1]);if(d<=v&&a&&Math.abs(l)>1)return N;if(d>=m&&a&&Math.abs(l)>1)return x;if(l&&Math.abs(g>=0&&E>=0?E-g:b-h)>1)return s;if(d<=v)return N;if(d>m)return x;if(l)return Math.abs(l)>1?s:(u+s+l)%u}},Y=function(n,e,t){var r=I(n.map(function(n){return n.node}).filter(function(n){var e,r=null===(e=b(n))||void 0===e?void 0:e.autofocus;return n.autofocus||void 0!==r&&"false"!==r||t.indexOf(n)>=0}));return r&&r.length?V(r):V(I(e))},J=function(n,e){return void 0===e&&(e=[]),e.push(n),n.parentNode&&J(n.parentNode.host||n.parentNode,e),e},X=function(n,e){for(var t=J(n),r=J(e),o=0;o<t.length;o+=1){var u=t[o];if(r.indexOf(u)>=0)return u}return!1},$=function(n,e,t){var r=f(n),o=f(e),u=r[0],i=!1;return o.filter(Boolean).forEach(function(n){i=X(i||n,n)||i,t.filter(Boolean).forEach(function(n){var e=X(u,n);e&&(i=!i||P(e,i)?e:X(e,i))})}),i},nn=function(n,e){return n.reduce(function(n,t){return n.concat(L(d(t.querySelectorAll("[".concat(a.GV,"]"))).map(function(n){return D([n])}).reduce(function(n,e){return n.concat(e)},[]),e))},[])},ne=function(n,e){var t=new Map;return e.forEach(function(n){return t.set(n.node,n)}),n.map(function(n){return t.get(n)}).filter(N)},nt=function(n,e){var t=W(f(n).length>0?document:s(n).ownerDocument),r=q(n).filter(x),o=$(t||n,n,r),u=new Map,i=C(r,u),c=i.filter(function(n){return x(n.node)});if(c[0]){var a=C([o],u).map(function(n){return n.node}),d=ne(a,c),l=d.map(function(n){return n.node}),v=d.filter(function(n){return n.tabIndex>=0}).map(function(n){return n.node}),m=Q(l,v,a,t,e);if(m===K){var p=Y(i,v,nn(r,u))||Y(i,l,nn(r,u));return p?{node:p}:void console.warn("focus-lock: cannot find any node to move focus into")}return void 0===m?m:d[m]}},nr=0,no=!1,nu=function(n,e,t){void 0===t&&(t={});var r=nt(n,e);if(!no&&r){if(nr>2){console.error("FocusLock: focus-fighting detected. Only one focus management system could be active. See https://github.com/theKashey/focus-lock/#focus-fighting"),no=!0,setTimeout(function(){no=!1},1);return}nr++,B(r.node,t.focusOptions),nr--}};function ni(n){if(!n)return null;if("undefined"==typeof WeakRef)return function(){return n||null};var e=n?new WeakRef(n):null;return function(){return(null==e?void 0:e.deref())||null}}var nc=function(n){if(!n)return null;for(var e=[],t=n;t&&t!==document.body;)e.push({current:ni(t),parent:ni(t.parentElement),left:ni(t.previousElementSibling),right:ni(t.nextElementSibling)}),t=t.parentElement;return{element:ni(n),stack:e,ownerDocument:n.ownerDocument}},na=function(n){if(n)for(var e,t,r,o,u,i=n.stack,c=n.ownerDocument,a=new Map,d=0;d<i.length;d++){var f=i[d],s=null===(e=f.parent)||void 0===e?void 0:e.call(f);if(s&&c.contains(s)){for(var l=null===(t=f.left)||void 0===t?void 0:t.call(f),v=f.current(),m=s.contains(v)?v:void 0,p=null===(r=f.right)||void 0===r?void 0:r.call(f),h=T([s],a),b=null!==(u=null!==(o=null!=m?m:null==l?void 0:l.nextElementSibling)&&void 0!==o?o:p)&&void 0!==u?u:l;b;){for(var y=0;y<h.length;y++){var g=h[y];if(null==b?void 0:b.contains(g.node))return g.node}b=b.nextElementSibling}if(h.length)return h[0].node}}},nd=function(n){var e=nc(n);return function(){return na(e)}},nf=function(n){var e=q(n).filter(x),t=k(D([$(n,n,e)],!0),!0,!0),r=D(e,!1);return t.map(function(n){var e=n.node;return{node:e,index:n.index,lockItem:r.indexOf(e)>=0,guard:w(e)}})},ns=function(n,e,t){if(!n||!e)return console.error("no element or scope given"),{};var r=f(e);if(r.every(function(e){return!P(e,n)}))return console.error("Active element is not contained in the scope"),{};var o=t?T(r,new Map):C(r,new Map),u=o.findIndex(function(e){return e.node===n});if(-1!==u)return{prev:o[u-1],next:o[u+1],first:o[0],last:o[o.length-1]}},nl=function(n,e){var t=e?T(f(n),new Map):C(f(n),new Map);return{first:t[0],last:t[t.length-1]}},nv=function(n,e,t){void 0===e&&(e={});var r,o=(r=e,Object.assign({scope:document.body,cycle:!0,onlyTabbable:!0},r)),u=ns(n,o.scope,o.onlyTabbable);if(u){var i=t(u,o.cycle);i&&B(i.node,o.focusOptions)}},nm=function(n,e,t){var r,o=nl(n,null===(r=e.onlyTabbable)||void 0===r||r)[t];o&&B(o.node,e.focusOptions)};function np(n){setTimeout(n,1)}var nh=function(n){return n&&"current"in n?n.current:n},nb=t(83199),ny=function(){return document&&document.activeElement===document.body},ng=null,nE=null,nw=function(){return null},nx=null,nN=!1,nO=!1,nk=function(n,e){nx={observerNode:n,portaledElement:e}};function nA(n,e,t,r){var o=null,u=n;do{var i=r[u];if(i.guard)i.node.dataset.focusAutoGuard&&(o=i);else if(i.lockItem){if(u!==n)return;o=null}else break}while((u+=t)!==e);o&&(o.node.tabIndex=0)}var nM=function(n){return C(n,new Map)},nF=function(){var n=!1;if(ng){var e=ng,t=e.observed,r=e.persistentFocus,o=e.autoFocus,u=e.shards,i=e.crossFrame,c=e.focusOptions,a=e.noFocusGuards,d=t||nx&&nx.portaledElement;if(ny()&&nE&&nE!==document.body&&(!document.body.contains(nE)||!nM([(l=nE).parentNode]).some(function(n){return n.node===l}))){var f=nw();f&&f.focus()}var s=document&&document.activeElement;if(d){var l,v=[d].concat(u.map(nh).filter(Boolean));if((!s||(ng.whiteList||function(){return!0})(s))&&(r||function(){if(!(i?!!nN:"meanwhile"===nN)||!a||!nE||nO)return!1;var n=nM(v),e=n.findIndex(function(n){return n.node===nE});return 0===e||e===n.length-1}()||!(ny()||_())||!nE&&o)&&(d&&!(U(v)||s&&v.some(function(n){return function n(e,t,r){return t&&(t.host===e&&(!t.activeElement||r.contains(t.activeElement))||t.parentNode&&n(e,t.parentNode,r))}(s,n,n)})||nx&&nx.portaledElement===s)&&(document&&!nE&&s&&!o?(s.blur&&s.blur(),document.body.focus()):(n=nu(v,nE,{focusOptions:c}),nx={})),(nE=document&&document.activeElement)!==document.body&&(nw=nd(nE)),nN=!1),document&&s!==document.activeElement&&document.querySelector("[data-focus-auto-guard]")){var m=document&&document.activeElement,p=nf(v),h=p.map(function(n){return n.node}).indexOf(m);h>-1&&(p.filter(function(n){var e=n.guard,t=n.node;return e&&t.dataset.focusAutoGuard}).forEach(function(n){return n.node.removeAttribute("tabIndex")}),nA(h,p.length,1,p),nA(h,-1,-1,p))}}}return n},nS=function(n){nF()&&n&&(n.stopPropagation(),n.preventDefault())},nD=function(){return np(nF)},nL=function(){nO=!0},nI=function(){nO=!1,nN="just",np(function(){nN="meanwhile"})},nT=function(){document.addEventListener("focusin",nS),document.addEventListener("focusout",nD),window.addEventListener("focus",nL),window.addEventListener("blur",nI)},nC=function(){document.removeEventListener("focusin",nS),document.removeEventListener("focusout",nD),window.removeEventListener("focus",nL),window.removeEventListener("blur",nI)},nP={moveFocusInside:nu,focusInside:U,focusNextElement:function(n,e){void 0===e&&(e={}),nv(n,e,function(n,e){var t=n.next,r=n.first;return t||e&&r})},focusPrevElement:function(n,e){void 0===e&&(e={}),nv(n,e,function(n,e){var t=n.prev,r=n.last;return t||e&&r})},focusFirstElement:function(n,e){void 0===e&&(e={}),nm(n,e,"first")},focusLastElement:function(n,e){void 0===e&&(e={}),nm(n,e,"last")},captureFocusRestore:nd};nb.qy.assignSyncMedium(function(n){var e=n.target,t=n.currentTarget;t.contains(e)||nk(t,e)}),nb.DA.assignMedium(nD),nb.zC.assignMedium(function(n){return n(nP)});var nR=(r=function(n){return n.filter(function(n){return!n.disabled})},o=function(n){var e=n.slice(-1)[0];e&&!ng&&nT();var t=ng,r=t&&e&&e.id===t.id;ng=e,!t||r||(t.onDeactivation(),n.filter(function(n){return n.id===t.id}).length||t.returnFocus(!e)),e?(nE=null,r&&t.observed===e.observed||e.onActivation(nP),nF(!0),np(nF)):(nC(),nE=null)},function(n){var e,t=[];function a(){o(e=r(t.map(function(n){return n.props})))}var d=function(r){function o(){return r.apply(this,arguments)||this}(0,i.Z)(o,r),o.peek=function(){return e};var c=o.prototype;return c.componentDidMount=function(){t.push(this),a()},c.componentDidUpdate=function(){a()},c.componentWillUnmount=function(){var n=t.indexOf(this);t.splice(n,1),a()},c.render=function(){return u.createElement(n,this.props)},o}(u.PureComponent);return(0,c.Z)(d,"displayName","SideEffect("+(n.displayName||n.name||"Component")+")"),d})(function(){return null})},83199:function(n,e,t){t.d(e,{DA:function(){return u},qy:function(){return o},sH:function(){return c},zC:function(){return i}});var r=t(87122),o=(0,r.s)({},function(n){return{target:n.target,currentTarget:n.currentTarget}}),u=(0,r.s)(),i=(0,r.s)(),c=(0,r._)({async:!0,ssr:"undefined"!=typeof document})},96118:function(n,e,t){t.d(e,{Y:function(){return M}});var r=t(97582),o=t(67294),u=t(13974),i=t(30376).Z,c=(0,t(87122)._)(),a="data-focus-on-hidden",d={preventScroll:!0},f=o.forwardRef(function(n,e){var t=o.useState(!1),a=t[0],f=t[1],s=n.children,l=n.autoFocus,v=n.shards,m=n.crossFrame,p=n.enabled,h=void 0===p||p,b=n.scrollLock,y=n.focusLock,g=n.returnFocus,E=n.inert,w=n.allowPinchZoom,x=n.sideCar,N=n.className,O=n.shouldIgnore,k=n.preventScrollOnFocus,A=n.style,M=n.as,F=n.gapMode,S=(0,r._T)(n,["children","autoFocus","shards","crossFrame","enabled","scrollLock","focusLock","returnFocus","inert","allowPinchZoom","sideCar","className","shouldIgnore","preventScrollOnFocus","style","as","gapMode"]),D=a.onActivation,L=a.onDeactivation,I=(0,r._T)(a,["onActivation","onDeactivation"]),T=(0,r.pi)((0,r.pi)({},I),{as:M,style:A,sideCar:x,shards:v,allowPinchZoom:w,gapMode:F,inert:E,enabled:h&&(void 0===b||b)});return o.createElement(o.Fragment,null,o.createElement(i,{ref:e,sideCar:x,disabled:!(a&&h&&(void 0===y||y)),returnFocus:void 0===g||g,autoFocus:l,shards:v,crossFrame:m,onActivation:D,onDeactivation:L,className:N,whiteList:O,lockProps:T,focusOptions:k?d:void 0,as:u.f},s),h&&o.createElement(x,(0,r.pi)({},S,{sideCar:c,setLockProps:f,shards:v})))}),s=t(66781),l=t(17742),v=t(83199);(0,s.L)(v.sH,l.Z),t(49310);var m=new WeakMap,p=new WeakMap,h={},b=0,y=function(n){return n&&(n.host||y(n.parentNode))},g=function(n,e,t,r){var o=(Array.isArray(n)?n:[n]).map(function(n){if(e.contains(n))return n;var t=y(n);return t&&e.contains(t)?t:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n});h[t]||(h[t]=new WeakMap);var u=h[t],i=[],c=new Set,a=new Set(o),d=function(n){!n||c.has(n)||(c.add(n),d(n.parentNode))};o.forEach(d);var f=function(n){!n||a.has(n)||Array.prototype.forEach.call(n.children,function(n){if(c.has(n))f(n);else try{var e=n.getAttribute(r),o=null!==e&&"false"!==e,a=(m.get(n)||0)+1,d=(u.get(n)||0)+1;m.set(n,a),u.set(n,d),i.push(n),1===a&&o&&p.set(n,!0),1===d&&n.setAttribute(t,"true"),o||n.setAttribute(r,"true")}catch(e){console.error("aria-hidden: cannot operate on ",n,e)}})};return f(e),c.clear(),b++,function(){i.forEach(function(n){var e=m.get(n)-1,o=u.get(n)-1;m.set(n,e),u.set(n,o),e||(p.has(n)||n.removeAttribute(r),p.delete(n)),o||n.removeAttribute(t)}),--b||(m=new WeakMap,m=new WeakMap,p=new WeakMap,h={})}},E=function(n,e,t){void 0===t&&(t="data-aria-hidden");var r=Array.from(Array.isArray(n)?n:[n]),o=e||("undefined"==typeof document?null:(Array.isArray(n)?n[0]:n).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),g(r,o,t,"aria-hidden")):function(){return null}},w=(0,t(6525).Ws)(),x="\n ["+a+"] {\n   pointer-events: none !important;\n }\n",N=function(){return o.createElement(w,{styles:x})},O=function(n){return"current"in n?n.current:n},k=(0,s.L)(c,function(n){var e=n.setLockProps,t=n.onEscapeKey,u=n.onClickOutside,i=n.shards,c=n.onActivation,d=n.onDeactivation,f=n.noIsolation,s=(0,o.useState)(void 0),l=s[0],v=s[1],m=(0,o.useRef)(null),p=(0,o.useRef)(0);return o.useEffect(function(){var n=function(n){!n.defaultPrevented&&("Escape"===n.code||"Escape"===n.key||27===n.keyCode)&&t&&t(n)},e=function(n){!(n.defaultPrevented||n.target===m.current||n instanceof MouseEvent&&0!==n.button||i&&i.map(O).some(function(e){return e&&e.contains(n.target)||e===n.target}))&&u&&u(n)},r=function(n){e(n),p.current=n.touches.length},o=function(n){p.current=n.touches.length};if(l)return document.addEventListener("keydown",n),document.addEventListener("mousedown",e),document.addEventListener("touchstart",r),document.addEventListener("touchend",o),function(){document.removeEventListener("keydown",n),document.removeEventListener("mousedown",e),document.removeEventListener("touchstart",r),document.removeEventListener("touchend",o)}},[l,u,t]),(0,o.useEffect)(function(){if(l)return c&&c(l),function(){d&&d()}},[!!l]),(0,o.useEffect)(function(){var n=function(){return null},t=!1;return e({onMouseDown:function(n){m.current=n.target},onTouchStart:function(n){m.current=n.target},onActivation:function(e){f||(n=E((0,r.pr)([e],(i||[]).map(O)),document.body,a)),v(function(){return e})},onDeactivation:function(){n(),t||v(null)}}),function(){t=!0,e(!1)}},[]),o.createElement(N,null)}),A=function(n){return o.createElement(k,(0,r.pi)({},n))},M=o.forwardRef(function(n,e){return o.createElement(f,(0,r.pi)({},n,{ref:e,sideCar:A}))})}}]);