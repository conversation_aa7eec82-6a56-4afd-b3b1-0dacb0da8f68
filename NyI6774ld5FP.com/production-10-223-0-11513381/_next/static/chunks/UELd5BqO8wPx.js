"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6957],{46957:function(n,e,t){t.d(e,{cZ:function(){return k},t9:function(){return w}});var r=t(67294),o=t(14921),u=t(55152),i=t(69695),a=t(40884),c=t(61331),l=t(24683),f=t(30836),s=t(97787),d=t(45697),v=t.n(d);function p(){return(p=Object.assign||function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n}).apply(this,arguments)}function y(n,e){if(null==n)return{};var t,r,o={},u=Object.keys(n);for(r=0;r<u.length;r++)t=u[r],e.indexOf(t)>=0||(o[t]=n[t]);return o}var m=["as","isOpen"],b=["allowPinchZoom","as","dangerouslyBypassFocusLock","dangerouslyBypassScrollLock","initialFocusRef","onClick","onDismiss","onKeyDown","onMouseDown","unstable_lockFocusAcrossFrames"],h=["as","onClick","onKeyDown"];v().bool,v().bool,v().bool,v().func;var w=(0,r.forwardRef)(function(n,e){var t=n.as,u=n.isOpen,i=void 0===u||u,c=y(n,m);return(0,a.kG)("dialog"),(0,r.useEffect)(function(){i?window.__REACH_DISABLE_TOOLTIPS=!0:window.requestAnimationFrame(function(){window.__REACH_DISABLE_TOOLTIPS=!1})},[i]),i?(0,r.createElement)(o.h,{"data-reach-dialog-wrapper":""},(0,r.createElement)(g,p({ref:e,as:void 0===t?"div":t},c))):null}),g=(0,r.forwardRef)(function(n,e){var t=n.allowPinchZoom,o=n.as,a=n.dangerouslyBypassFocusLock,d=n.dangerouslyBypassScrollLock,v=n.initialFocusRef,m=n.onClick,h=n.onDismiss,w=void 0===h?i.Z:h,g=n.onKeyDown,k=n.onMouseDown,E=n.unstable_lockFocusAcrossFrames,A=y(n,b),C=(0,r.useRef)(null),O=(0,r.useRef)(null),S=(0,c.e)(O,e),_=(0,r.useCallback)(function(){v&&v.current&&v.current.focus()},[v]);return(0,r.useEffect)(function(){var n,e,t,r;return O.current?(n=O.current,e=[],t=[],r=(0,u.r)(n),n?(Array.prototype.forEach.call(r.querySelectorAll("body > *"),function(r){if(r!==(null==(o=n.parentNode)?void 0:null==(u=o.parentNode)?void 0:u.parentNode)){var o,u,i=r.getAttribute("aria-hidden");(null===i||"false"===i)&&(e.push(i),t.push(r),r.setAttribute("aria-hidden","true"))}}),function(){t.forEach(function(n,t){var r=e[t];null===r?n.removeAttribute("aria-hidden"):n.setAttribute("aria-hidden",r)})}):i.Z):void 0},[]),(0,r.createElement)(f.ZP,{autoFocus:!0,returnFocus:!0,onActivation:_,disabled:void 0!==a&&a,crossFrame:null==E||E},(0,r.createElement)(s.Z,{allowPinchZoom:t,enabled:!(void 0!==d&&d)},(0,r.createElement)(void 0===o?"div":o,p({},A,{ref:S,"data-reach-dialog-overlay":"",onClick:(0,l.M)(m,function(n){C.current===n.target&&(n.stopPropagation(),w(n))}),onKeyDown:(0,l.M)(g,function(n){"Escape"===n.key&&(n.stopPropagation(),w(n))}),onMouseDown:(0,l.M)(k,function(n){C.current=n.target})}))))}),k=(0,r.forwardRef)(function(n,e){var t=n.as,o=n.onClick;n.onKeyDown;var u=y(n,h);return(0,r.createElement)(void 0===t?"div":t,p({"aria-modal":"true",role:"dialog",tabIndex:-1},u,{ref:e,"data-reach-dialog-content":"",onClick:(0,l.M)(o,function(n){n.stopPropagation()})}))})},14921:function(n,e,t){t.d(e,{h:function(){return l}});var r=t(67294),o=t(96637),u=t(12459),i=t(73935),a=["unstable_skipInitialRender"],c=function(n){var e=n.children,t=n.type,a=void 0===t?"reach-portal":t,c=n.containerRef,l=(0,r.useRef)(null),f=(0,r.useRef)(null),s=(0,u.N)();return(0,o.L)(function(){if(l.current){var n=l.current.ownerDocument,e=(null==c?void 0:c.current)||n.body;return f.current=null==n?void 0:n.createElement(a),e.appendChild(f.current),s(),function(){f.current&&e&&e.removeChild(f.current)}}},[a,s,c]),f.current?(0,i.createPortal)(e,f.current):(0,r.createElement)("span",{ref:l})},l=function(n){var e=n.unstable_skipInitialRender,t=function(n,e){if(null==n)return{};var t,r,o={},u=Object.keys(n);for(r=0;r<u.length;r++)t=u[r],e.indexOf(t)>=0||(o[t]=n[t]);return o}(n,a),o=(0,r.useState)(!1),u=o[0],i=o[1];return((0,r.useEffect)(function(){e&&i(!0)},[e]),e&&!u)?null:(0,r.createElement)(c,t)};e.Z=l},12769:function(n,e,t){t.d(e,{N:function(){return r}});function r(){return!!("undefined"!=typeof window&&window.document&&window.document.createElement)}},24683:function(n,e,t){t.d(e,{M:function(){return r}});function r(n,e){return function(t){if(n&&n(t),!t.defaultPrevented)return e(t)}}},61331:function(n,e,t){t.d(e,{e:function(){return i}});var r=t(67294),o=t(85777);function u(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=Array(e);t<e;t++)r[t]=n[t];return r}function i(){for(var n=arguments.length,e=Array(n),t=0;t<n;t++)e[t]=arguments[t];return(0,r.useCallback)(function(n){for(var t,r=function(n,e){var t;if("undefined"==typeof Symbol||null==n[Symbol.iterator]){if(Array.isArray(n)||(t=function(n,e){if(n){if("string"==typeof n)return u(n,void 0);var t=Object.prototype.toString.call(n).slice(8,-1);if("Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t)return Array.from(n);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return u(n,void 0)}}(n))){t&&(n=t);var r=0;return function(){return r>=n.length?{done:!0}:{done:!1,value:n[r++]}}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}return(t=n[Symbol.iterator]()).next.bind(t)}(e);!(t=r()).done;)!function(n,e){if(null!=n){if((0,o.mf)(n))n(e);else try{n.current=e}catch(t){throw Error('Cannot assign value "'+e+'" to ref "'+n+'"')}}}(t.value,n)},e)}},40884:function(n,e,t){function r(n){}t.d(e,{kG:function(){return r}}),t(67294)},69695:function(n,e,t){t.d(e,{Z:function(){return r}});function r(){}},55152:function(n,e,t){t.d(e,{r:function(){return o}});var r=t(12769);function o(n){return(0,r.N)()?n?n.ownerDocument:document:null}},85777:function(n,e,t){function r(n){return"boolean"==typeof n}function o(n){return!!(n&&"[object Function]"==({}).toString.call(n))}t.d(e,{jn:function(){return r},mf:function(){return o}})},12459:function(n,e,t){t.d(e,{N:function(){return o}});var r=t(67294);function o(){var n=(0,r.useState)(Object.create(null))[1];return(0,r.useCallback)(function(){n(Object.create(null))},[])}},96637:function(n,e,t){t.d(e,{L:function(){return o}});var r=t(67294),o=(0,t(12769).N)()?r.useLayoutEffect:r.useEffect}}]);