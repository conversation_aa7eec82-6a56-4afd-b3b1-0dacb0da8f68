(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2967],{57082:function(e,t,n){"use strict";n.d(t,{Z:function(){return V}});var o=n(45987),a=n(87462),i=n(86854),r=n(67294),s=n(19521),l=n(40338),c=n(38722),d=n(24137),u=n(77786),p=n(36665),m=n(27814),f=n(97787),h=n(97763),g=n(24345),v=n(46675),y=n(16546),b=n(73106),w=n(93795),M=n(61203),_=(0,r.createContext)(void 0);function x(){var e=(0,r.useContext)(_);if(void 0===e)throw Error("useModalContext must be within ModalContext Provider");return e}var C=n(72287),E=n(77376),z=["ref"],S=s.default.div.withConfig({displayName:"Modal__IconStyle",componentId:"sc-18c5d2p-0"})(["width:var(--gds-modal-leading-icon-size);height:var(--gds-modal-leading-icon-size);"]),Z={warning:r.createElement(S,{as:M.Z}),error:r.createElement(S,{as:b.Z,fillColor:"danger"})},k=function(e){var t=e.role,n=e.ariaLabel,o=e.isOpen,s=void 0!==o&&o,d=e.onClose,u=e.children,p=e.variant,m=void 0===p?"standard":p,h=e.size,g=void 0===h?"md":h,v=e.hasImage,y=void 0!==v&&v,b=e.mobileMaxViewportWidth,w=(0,l.I)({open:s,setOpen:function(e){e||d()}}),M=(0,r.useState)(null),x=(0,i.Z)(M,2),C=x[0],z=x[1],S=(0,r.useState)(!1),Z=(0,i.Z)(S,2),k=Z[0],I=Z[1],N=(0,r.useMemo)(function(){return{variant:m,hasImage:y,hasScroll:k,setHasScroll:I,size:g,mobileMaxViewportWidth:b}},[m,y,k,g,b]),q=(0,E.H)();return((0,r.useEffect)(function(){if(C){var e=new ResizeObserver(function(){var e=getComputedStyle(C,null).overflowY;C.scrollHeight>C.clientHeight&&("auto"===e||"scroll"===e)?I(!0):I(!1)});return e.observe(C),function(){return e.disconnect()}}},[I,C]),(0,c.Kw)(w,"mounted"))?r.createElement(_.Provider,{value:N},r.createElement(f.Z,{forwardProps:!0,allowPinchZoom:!0,enabled:s,ref:function(e){return z(e)}},r.createElement(O,{store:w,portalElement:q,role:void 0===t?"dialog":t,open:s,onClose:d,backdrop:!1,preventBodyScroll:!1,render:function(e){return r.createElement(W,{$open:s},r.createElement("div",(0,a.Z)({},e,{"aria-label":n},n?{"aria-labelledby":void 0}:{})))},"aria-modal":"true",$size:g,$hasScroll:k,$mobileMaxViewportWidth:b},u))):null},I=function(){var e=x().variant;return"standard"===e?null:Z[e]},W=s.default.div.withConfig({displayName:"Modal__Overlay",componentId:"sc-18c5d2p-1"})(["position:fixed;inset:0;display:",";align-items:center;justify-content:center;padding:"," 0;overflow-y:auto;background-color:",";transition:opacity 400ms cubic-bezier(0.16,1,0.3,1);"],function(e){return e.$open?"flex":"none"},h.W0.amphitheatre,function(e){return e.theme.colors.base.overlay}),O=(0,s.default)(u.Vq).withConfig({displayName:"Modal__StyledModal",componentId:"sc-18c5d2p-2"})(["--gds-modal-bg-color:",";--gds-modal-scroll-border-color:",";--gds-modal-padding-size:",";--gds-modal-gap:",";--gds-modal-leading-icon-size:1.5em;",";position:relative;display:flex;flex-direction:column;width:80vw;max-height:100%;overflow:auto;background-color:var(--gds-modal-bg-color);animation:150ms cubic-bezier(0.16,1,0.3,1) 0s 1 normal none running modalAnimation;","{--gds-modal-padding-size:",";--gds-modal-leading-icon-size:2em;width:",";}@keyframes modalAnimation{0%{transform:translateY(2%) scale(0.96);opacity:0;}100%{transform:translateY(0) scale(1);opacity:1;}}"],function(e){return e.theme.colors.base.bg},function(e){return e.theme.base.borderMidtone},h.W0.amphitheatre,h.W0.auditorium,g.X.level4,function(e){return"@media not all and (max-width: ".concat(e.$mobileMaxViewportWidth,")")},h.W0.arena,function(e){return"sm"===e.$size?"400px":"md"===e.$size?"600px":"740px"}),N=s.default.header.withConfig({displayName:"Modal__StyledHeader",componentId:"sc-18c5d2p-3"})(["position:sticky;top:0;display:flex;flex-direction:column;gap:",";padding:var(--gds-modal-padding-size) var(--gds-modal-padding-size) ",";background-color:var(--gds-modal-bg-color);",""],h.W0.club,h.W0.auditorium,function(e){return e.$hasScroll&&(0,s.css)(["border-bottom:1px solid var(--gds-modal-scroll-border-color);"])}),q=(0,s.default)(p.E).withConfig({displayName:"Modal__StyledTitle",componentId:"sc-18c5d2p-4"})([""," margin:0;",""],v.Cg.matterhorn,function(e){return e.$hasRightOffset&&(0,s.css)(["padding-right:calc("," + ",");"],y.EA.total,h.W0.auditorium)}),L=(0,s.default)(m.B).withConfig({displayName:"Modal__StyledDescription",componentId:"sc-18c5d2p-5"})([""," margin:0;color:",";"],v.Cg.rainier,function(e){return e.theme.colors.text.primary}),$=s.default.div.withConfig({displayName:"Modal__StyledContent",componentId:"sc-18c5d2p-6"})(["padding:"," var(--gds-modal-padding-size) ",";background-color:var(--gds-modal-bg-color);"],function(e){return e.$hasScroll?h.W0.auditorium:0},h.W0.auditorium),T=s.default.img.withConfig({displayName:"Modal__StyledImage",componentId:"sc-18c5d2p-7"})(["width:100%;aspect-ratio:16 / 9;margin-top:0;margin-left:0;object-fit:cover;"]),j=s.default.div.withConfig({displayName:"Modal__CloseButtonWrapper",componentId:"sc-18c5d2p-8"})(["position:sticky;top:0;z-index:1;"]),A=(0,s.default)(C.Z).withConfig({displayName:"Modal__StyledIconButton",componentId:"sc-18c5d2p-9"})(["--gds-modal-icon-size:32px;position:absolute;top:var(--gds-modal-padding-size);right:var(--gds-modal-padding-size);width:var(--gds-modal-icon-size);height:var(--gds-modal-icon-size);"," &::after{position:absolute;inset:calc(("," - var(--gds-modal-icon-size)) / 2 * -1);width:",";height:",';content:"";}'],function(e){return e.$dark&&(0,s.css)(["background-color:",";"],function(e){return e.theme.base.bgInverse})},y.EA.total,y.EA.total,y.EA.total),P=s.default.div.withConfig({displayName:"Modal__StyledActions",componentId:"sc-18c5d2p-10"})(["position:sticky;bottom:0;display:flex;flex-direction:column;gap:",";justify-content:flex-end;padding:"," var(--gds-modal-padding-size) var(--gds-modal-padding-size);background-color:var(--gds-modal-bg-color);"," ","{flex-direction:row-reverse;gap:",";justify-content:flex-start;}"],h.W0.club,h.W0.auditorium,function(e){return e.$hasScroll&&(0,s.css)(["border-top:1px solid var(--gds-modal-scroll-border-color);"])},function(e){return"@media not all and (max-width: ".concat(e.$mobileMaxViewportWidth,")")},h.W0.auditorium);k.Header=function(e){var t=e.children,n=e.className,o=x().hasScroll;return r.createElement(N,{$hasScroll:o,className:n},r.createElement(I,null),t)},k.Title=function(e){var t=e.children,n=e.className,o=x().variant;return r.createElement(q,{className:n,$hasRightOffset:"standard"===o},t)},k.Content=function(e){var t=e.children,n=e.className,o=x().hasScroll;return r.createElement($,{className:n,$hasScroll:o,tabIndex:o?0:-1},t)},k.Description=L,k.Image=function(e){return r.createElement(T,e)},k.CloseButton=function(e){var t=e.label,n=e.disabled,i=void 0!==n&&n,s=x().hasImage;return r.createElement(j,null,r.createElement(d.S,{render:function(e){e.ref;var n=(0,o.Z)(e,z);return r.createElement(A,(0,a.Z)({},n,{variant:s?"primary":"ghost",label:t,icon:r.createElement(w.Z,{size:"1em"}),$dark:s,disabled:i}))}}))},k.Actions=function(e){var t=e.children,n=e.className,o=x(),a=o.hasScroll,i=o.mobileMaxViewportWidth;return r.createElement(P,{$hasScroll:a,$mobileMaxViewportWidth:i,className:n},t)};var V=k},61203:function(e,t,n){"use strict";var o=n(87462),a=n(67294),i=(0,n(19521).default)(function(e){return a.createElement("svg",(0,o.Z)({fill:"none",viewBox:"0 0 24 24",width:e.size||"2.5em",height:e.size||"2.5em","aria-hidden":!0,className:e.className},e),a.createElement("path",{fill:"#FFB932",d:"M12 2 1 22h22z"}),a.createElement("path",{fill:"#121212",d:"M11 9v7h2V9zm0 8.25v2h2v-2z"}))}).withConfig({displayName:"WarningFilledIcon",componentId:"sc-128ktdd-0"})(["flex-shrink:0;"]);t.Z=i},41143:function(e){"use strict";e.exports=function(e,t,n,o,a,i,r,s){if(!e){var l;if(void 0===t)l=Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[n,o,a,i,r,s],d=0;(l=Error(t.replace(/%s/g,function(){return c[d++]}))).name="Invariant Violation"}throw l.framesToPop=1,l}}},80973:function(e,t,n){var o=n(71169),a=function(e){var t="",n=Object.keys(e);return n.forEach(function(a,i){var r,s=e[a];r=a=o(a),/[height|width]$/.test(r)&&"number"==typeof s&&(s+="px"),!0===s?t+=a:!1===s?t+="not "+a:t+="("+a+": "+s+")",i<n.length-1&&(t+=" and ")}),t};e.exports=function(e){var t="";return"string"==typeof e?e:e instanceof Array?(e.forEach(function(n,o){t+=a(n),o<e.length-1&&(t+=", ")}),t):a(e)}},92703:function(e,t,n){"use strict";var o=n(50414);function a(){}function i(){}i.resetWarningCache=a,e.exports=function(){function e(e,t,n,a,i,r){if(r!==o){var s=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:a};return n.PropTypes=n,n}},45697:function(e,t,n){e.exports=n(92703)()},50414:function(e){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},87784:function(e,t,n){"use strict";var o=n(87462),a=n(94578),i=n(97326),r=n(4942),s=n(67294),l=n(45697),c=n.n(l),d=n(41143),u=n.n(d),p=n(80973),m=n.n(p),f=function(){function e(e,t,n){var o=this;this.nativeMediaQueryList=e.matchMedia(t),this.active=!0,this.cancellableListener=function(){o.matches=o.nativeMediaQueryList.matches,o.active&&n.apply(void 0,arguments)},this.nativeMediaQueryList.addListener(this.cancellableListener),this.matches=this.nativeMediaQueryList.matches}return e.prototype.cancel=function(){this.active=!1,this.nativeMediaQueryList.removeListener(this.cancellableListener)},e}(),h=c().oneOfType([c().string,c().object,c().arrayOf(c().object.isRequired)]),g=function(e){function t(t){var n,a;return(n=e.call(this,t)||this,(0,r.Z)((0,i.Z)((0,i.Z)(n)),"queries",[]),(0,r.Z)((0,i.Z)((0,i.Z)(n)),"getMatches",function(){var e,t;return 1===(t=Object.keys(e=n.queries.reduce(function(e,t){var n,a=t.name,i=t.mqListener;return(0,o.Z)({},e,((n={})[a]=i.matches,n))},{}))).length&&"__DEFAULT__"===t[0]?e.__DEFAULT__:e}),(0,r.Z)((0,i.Z)((0,i.Z)(n)),"updateMatches",function(){var e=n.getMatches();n.setState(function(){return{matches:e}},n.onChange)}),!(!t.query&&!t.queries)||t.query&&t.queries||u()(!1),void 0!==t.defaultMatches&&t.query&&"boolean"!=typeof t.defaultMatches&&u()(!1),void 0!==t.defaultMatches&&t.queries&&"object"!=typeof t.defaultMatches&&u()(!1),"object"!=typeof window)?(a=void 0!==t.defaultMatches?t.defaultMatches:!!t.query||Object.keys(n.props.queries).reduce(function(e,t){var n;return(0,o.Z)({},e,((n={})[t]=!0,n))},{}),n.state={matches:a},(0,i.Z)(n)):(n.initialize(),n.state={matches:void 0!==n.props.defaultMatches?n.props.defaultMatches:n.getMatches()},n.onChange(),n)}(0,a.Z)(t,e);var n=t.prototype;return n.initialize=function(){var e=this,t=this.props.targetWindow||window;"function"!=typeof t.matchMedia&&u()(!1);var n=this.props.queries||{__DEFAULT__:this.props.query};this.queries=Object.keys(n).map(function(o){var a=n[o];return{name:o,mqListener:new f(t,"string"!=typeof a?m()(a):a,e.updateMatches)}})},n.componentDidMount=function(){this.initialize(),void 0!==this.props.defaultMatches&&this.updateMatches()},n.onChange=function(){var e=this.props.onChange;e&&e(this.state.matches)},n.componentWillUnmount=function(){this.queries.forEach(function(e){return e.mqListener.cancel()})},n.render=function(){var e=this.props,t=e.children,n=e.render,o=this.state.matches,a="object"==typeof o?Object.keys(o).some(function(e){return o[e]}):o;return n?a?n(o):null:t?"function"==typeof t?t(o):(!Array.isArray(t)||t.length)&&a?s.Children.only(t)&&"string"==typeof s.Children.only(t).type?s.Children.only(t):s.cloneElement(s.Children.only(t),{matches:o}):null:null},t}(s.Component);(0,r.Z)(g,"propTypes",{defaultMatches:c().oneOfType([c().bool,c().objectOf(c().bool)]),query:h,queries:c().objectOf(h),render:c().func,children:c().oneOfType([c().node,c().func]),targetWindow:c().object,onChange:c().func}),t.Z=g},71169:function(e){e.exports=function(e){return e.replace(/[A-Z]/g,function(e){return"-"+e.toLowerCase()}).toLowerCase()}}}]);